"""
Referral API Routes

This module defines API endpoints for the referral system including:
- Getting user's referral code
- Getting referral statistics
- Getting token balance
- Getting referral history
- Validating referral codes
"""

from typing import Any, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.auth_guard import UserAuthGuard, OrganizationAuthGuard
from app.core.database import get_db
from app.services.referral_service import ReferralService
from app.models.referral import EntityType
from app.models.user import User
from app.models.organization import Organization
from app.schemas.referral_schema import (
    ReferralCodeResponse,
    ReferralStatsResponse,
    ReferralHistoryResponse,
    TokenBalanceResponse,
    ValidateReferralCodeRequest,
    ValidateReferralCodeResponse,
    ReferralDashboardResponse,
    ReferralErrorResponse
)

# Create router
referral_router = APIRouter(prefix="/referral", tags=["Referral System"])

# Auth guards
user_auth_guard = UserAuthGuard()
org_auth_guard = OrganizationAuthGuard()


def get_entity_type(registration_type: str) -> EntityType:
    """Convert registration_type to EntityType enum."""
    return EntityType.USER if registration_type == "individual" else EntityType.ORGANIZATION


@referral_router.get(
    "/code",
    summary="Get User's Referral Code",
    description="Get the authenticated user's or organization's referral code. Creates one if it doesn't exist.",
    response_model=ReferralCodeResponse,
    responses={
        200: {"description": "Referral code retrieved successfully"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"}
    }
)
async def get_referral_code(
    current_user: Dict[str, Any] = Depends(user_auth_guard),
    db: Session = Depends(get_db)
):
    """Get or create referral code for the authenticated user/organization."""
    try:
        referral_service = ReferralService(db)

        # Determine entity type and details
        entity_type = get_entity_type(current_user["registration_type"])
        entity_id = current_user["user_id"]

        # Get entity name from database
        if entity_type == EntityType.USER:
            user = db.query(User).filter(User.id == entity_id).first()
            entity_name = user.full_name if user else "User"
        else:
            org = db.query(Organization).filter(Organization.id == entity_id).first()
            entity_name = org.organization_name if org else "Organization"
        
        # Get or create referral code
        referral_code = referral_service.get_or_create_referral_code(
            entity_type, entity_id, entity_name
        )
        
        return ReferralCodeResponse(
            success=True,
            referral_code=referral_code,
            message="Referral code retrieved successfully"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get referral code: {str(e)}")


@referral_router.get(
    "/stats",
    summary="Get Referral Statistics",
    description="Get comprehensive referral statistics for the authenticated user/organization.",
    response_model=ReferralStatsResponse,
    responses={
        200: {"description": "Referral statistics retrieved successfully"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"}
    }
)
async def get_referral_stats(
    current_user: Dict[str, Any] = Depends(user_auth_guard),
    db: Session = Depends(get_db)
):
    """Get referral statistics for the authenticated user/organization."""
    try:
        referral_service = ReferralService(db)
        
        # Determine entity type
        entity_type = get_entity_type(current_user["registration_type"])
        entity_id = current_user["user_id"]
        
        # Get referral statistics
        stats = referral_service.get_referral_stats(entity_type, entity_id)
        
        return ReferralStatsResponse(**stats)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get referral statistics: {str(e)}")


@referral_router.get(
    "/history",
    summary="Get Referral History",
    description="Get the history of people referred by the authenticated user/organization.",
    response_model=ReferralHistoryResponse,
    responses={
        200: {"description": "Referral history retrieved successfully"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"}
    }
)
async def get_referral_history(
    limit: int = Query(50, ge=1, le=100, description="Maximum number of referrals to return"),
    current_user: Dict[str, Any] = Depends(user_auth_guard),
    db: Session = Depends(get_db)
):
    """Get referral history for the authenticated user/organization."""
    try:
        referral_service = ReferralService(db)
        
        # Determine entity type
        entity_type = get_entity_type(current_user["registration_type"])
        entity_id = current_user["user_id"]
        
        # Get referral history
        history = referral_service.get_referral_history(entity_type, entity_id, limit)
        
        return ReferralHistoryResponse(
            success=True,
            referrals=history,
            total_count=len(history)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get referral history: {str(e)}")


@referral_router.get(
    "/tokens/balance",
    summary="Get Token Balance",
    description="Get the current token balance for the authenticated user/organization.",
    response_model=TokenBalanceResponse,
    responses={
        200: {"description": "Token balance retrieved successfully"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"}
    }
)
async def get_token_balance(
    current_user: Dict[str, Any] = Depends(user_auth_guard),
    db: Session = Depends(get_db)
):
    """Get token balance for the authenticated user/organization."""
    try:
        referral_service = ReferralService(db)
        
        # Determine entity type
        entity_type = EntityType.USER if current_user["registration_type"] == "individual" else EntityType.ORGANIZATION
        entity_id = current_user["user_id"]
        
        # Get token balance
        current_balance = referral_service.get_token_balance(entity_type, entity_id)
        
        # Calculate total earned (no spending functionality)
        total_earned = current_balance  # All tokens are earned, none spent
        total_spent = 0  # No token spending functionality
        
        return TokenBalanceResponse(
            success=True,
            current_balance=current_balance,
            total_earned=total_earned,
            total_spent=total_spent
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get token balance: {str(e)}")


@referral_router.post(
    "/validate",
    summary="Validate Referral Code",
    description="Validate if a referral code exists and is active. Does not create any referral relationship.",
    response_model=ValidateReferralCodeResponse,
    responses={
        200: {"description": "Referral code validation completed"},
        400: {"description": "Invalid request data"},
        500: {"description": "Internal server error"}
    }
)
async def validate_referral_code(
    request: ValidateReferralCodeRequest,
    db: Session = Depends(get_db)
):
    """Validate a referral code without authentication (for signup form validation)."""
    try:
        referral_service = ReferralService(db)
        
        # Validate referral code
        referral_code_obj = referral_service.validate_referral_code(request.referral_code)
        
        if referral_code_obj:
            # Get referrer information
            referrer_info = {
                "entity_type": referral_code_obj.entity_type.value,
                "entity_id": referral_code_obj.entity_id
            }
            
            # Get referrer name
            if referral_code_obj.entity_type == EntityType.USER:
                from app.models.user import User
                user = db.query(User).filter(User.id == referral_code_obj.entity_id).first()
                referrer_info["name"] = user.full_name if user else "Unknown User"
            else:
                from app.models.organization import Organization
                org = db.query(Organization).filter(Organization.id == referral_code_obj.entity_id).first()
                referrer_info["name"] = org.organization_name if org else "Unknown Organization"
            
            return ValidateReferralCodeResponse(
                success=True,
                is_valid=True,
                message="Referral code is valid",
                referrer_info=referrer_info
            )
        else:
            return ValidateReferralCodeResponse(
                success=True,
                is_valid=False,
                message="Referral code is invalid or expired"
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to validate referral code: {str(e)}")


@referral_router.get(
    "/dashboard",
    summary="Get Referral Dashboard Data",
    description="Get comprehensive referral dashboard data including stats, recent referrals, and token balance.",
    response_model=ReferralDashboardResponse,
    responses={
        200: {"description": "Dashboard data retrieved successfully"},
        401: {"description": "Authentication required"},
        500: {"description": "Internal server error"}
    }
)
async def get_referral_dashboard(
    current_user: Dict[str, Any] = Depends(user_auth_guard),
    db: Session = Depends(get_db)
):
    """Get comprehensive referral dashboard data."""
    try:
        referral_service = ReferralService(db)
        
        # Determine entity type
        entity_type = EntityType.USER if current_user["registration_type"] == "individual" else EntityType.ORGANIZATION
        entity_id = current_user["user_id"]
        
        # Get all data
        stats = referral_service.get_referral_stats(entity_type, entity_id)
        recent_referrals = referral_service.get_referral_history(entity_type, entity_id, 10)
        current_balance = referral_service.get_token_balance(entity_type, entity_id)
        
        return ReferralDashboardResponse(
            success=True,
            referral_code=stats["referral_code"],
            stats=ReferralStatsResponse(**stats),
            recent_referrals=recent_referrals,
            token_balance=TokenBalanceResponse(
                success=True,
                current_balance=current_balance,
                total_earned=current_balance,  # All tokens are earned
                total_spent=0  # No token spending functionality
            )
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get dashboard data: {str(e)}")
