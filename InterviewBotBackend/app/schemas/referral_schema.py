"""
Referral System Schemas

This module defines Pydantic schemas for referral-related API requests and responses.
"""

from typing import List, Optional
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum


class EntityType(str, Enum):
    """Entity type enumeration for API responses."""
    user = "user"
    organization = "organization"


class TokenTransactionType(str, Enum):
    """Token transaction type enumeration for API responses."""
    referral_bonus_referrer = "referral_bonus_referrer"
    referral_bonus_referee = "referral_bonus_referee"
    bonus_award = "bonus_award"


# Request Schemas

class ReferralCodeRequest(BaseModel):
    """Request schema for generating or retrieving referral code."""
    pass  # No additional fields needed, user info comes from auth


class ValidateReferralCodeRequest(BaseModel):
    """Request schema for validating a referral code."""
    referral_code: str = Field(..., min_length=12, max_length=12, description="Referral code to validate")

    @validator('referral_code')
    def validate_code_format(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('Referral code must be a non-empty string')

        # Remove whitespace and convert to uppercase
        normalized = v.strip().upper()

        # Basic format validation
        if len(normalized) != 12:
            raise ValueError('Referral code must be exactly 12 characters')

        return normalized


# Response Schemas

class ReferralCodeResponse(BaseModel):
    """Response schema for referral code operations."""
    success: bool
    referral_code: str = Field(..., description="The user's unique referral code")
    message: str = Field(default="Referral code retrieved successfully")


class ReferralStatsResponse(BaseModel):
    """Response schema for referral statistics."""
    success: bool = True
    referral_code: Optional[str] = Field(None, description="User's referral code")
    total_referrals: int = Field(0, description="Total number of people referred")
    successful_referrals: int = Field(0, description="Number of successful referrals (completed signup)")
    total_tokens_earned: int = Field(0, description="Total tokens earned from referrals")
    current_token_balance: int = Field(0, description="Current token balance")
    last_referral_date: Optional[str] = Field(None, description="Date of last referral (ISO format)")


class ReferralHistoryItem(BaseModel):
    """Schema for individual referral history item."""
    referee_name: str = Field(..., description="Name of the person who was referred")
    referee_type: EntityType = Field(..., description="Type of entity that was referred")
    referral_date: str = Field(..., description="Date when referral occurred (ISO format)")
    tokens_awarded: bool = Field(..., description="Whether tokens were awarded for this referral")
    tokens_awarded_at: Optional[str] = Field(None, description="Date when tokens were awarded (ISO format)")


class ReferralHistoryResponse(BaseModel):
    """Response schema for referral history."""
    success: bool = True
    referrals: List[ReferralHistoryItem] = Field(default_factory=list, description="List of referral history items")
    total_count: int = Field(0, description="Total number of referrals")


class TokenTransactionItem(BaseModel):
    """Schema for individual token transaction."""
    id: str = Field(..., description="Transaction ID")
    transaction_type: TokenTransactionType = Field(..., description="Type of transaction")
    amount: int = Field(..., description="Amount of tokens (positive for credit, negative for debit)")
    balance_after: int = Field(..., description="Token balance after this transaction")
    description: Optional[str] = Field(None, description="Description of the transaction")
    created_at: str = Field(..., description="Transaction date (ISO format)")
    reference_type: Optional[str] = Field(None, description="Type of reference (e.g., 'referral', 'purchase')")


class TokenTransactionHistoryResponse(BaseModel):
    """Response schema for token transaction history."""
    success: bool = True
    transactions: List[TokenTransactionItem] = Field(default_factory=list, description="List of token transactions")
    current_balance: int = Field(0, description="Current token balance")
    total_earned: int = Field(0, description="Total tokens earned")
    total_spent: int = Field(0, description="Total tokens spent")


class TokenBalanceResponse(BaseModel):
    """Response schema for token balance."""
    success: bool = True
    current_balance: int = Field(0, description="Current token balance")
    total_earned: int = Field(0, description="Total tokens earned from referrals")
    total_spent: int = Field(0, description="Total tokens spent (always 0 - no spending functionality)")


class ValidateReferralCodeResponse(BaseModel):
    """Response schema for referral code validation."""
    success: bool
    is_valid: bool = Field(..., description="Whether the referral code is valid")
    message: str = Field(..., description="Validation result message")
    referrer_info: Optional[dict] = Field(None, description="Information about the referrer (if valid)")


# Error Response Schemas

class ReferralErrorResponse(BaseModel):
    """Error response schema for referral operations."""
    success: bool = False
    error: str = Field(..., description="Error message")
    error_code: Optional[str] = Field(None, description="Error code for programmatic handling")


# Combined Response Schemas

class ReferralDashboardResponse(BaseModel):
    """Combined response schema for referral dashboard data."""
    success: bool = True
    referral_code: Optional[str] = Field(None, description="User's referral code")
    stats: ReferralStatsResponse = Field(..., description="Referral statistics")
    recent_referrals: List[ReferralHistoryItem] = Field(default_factory=list, description="Recent referrals (last 10)")
    token_balance: TokenBalanceResponse = Field(..., description="Token balance information")


# Registration Schema Updates (to be used in existing registration schemas)

class ReferralSignupData(BaseModel):
    """Schema for referral data during signup."""
    referral_code: Optional[str] = Field(None, min_length=12, max_length=12, description="Optional referral code")

    @validator('referral_code', pre=True)
    def validate_referral_code(cls, v):
        if v is None or v == "":
            return None

        if not isinstance(v, str):
            raise ValueError('Referral code must be a string')

        # Remove whitespace and convert to uppercase
        normalized = v.strip().upper()

        if len(normalized) != 12:
            raise ValueError('Referral code must be exactly 12 characters')

        return normalized
