"""
Razorpay Service

This module provides the service layer for Razorpay payment integration.
Handles order creation, payment verification, webhook processing, and transaction management.
"""

import hashlib
import hmac
import uuid
import razorpay
from razorpay.errors import BadRequestError, GatewayError, ServerError, SignatureVerificationError
import structlog
from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import settings
from app.models.subscription_plan import SubscriptionPlan
from app.models.order import Order, OrderStatus
from app.models.transaction import Transaction, TransactionStatus, TransactionType
from app.models.webhook_event import WebhookEvent, WebhookEventType, WebhookEventStatus
from app.models.audit_log import AuditLog, AuditAction, AuditEntityType
from app.models.user_subscription import UserSubscription
from app.models.organization_subscription import OrganizationSubscription
from app.schemas.user_schema import RegistrationType
from app.schemas.payment_schema import (
    OrderCreate, PaymentVerification
)
from app.utils.exceptions.payment_exceptions import (
    OrderCreationException,
    PaymentVerificationException,
    WebhookProcessingException,
    InvalidSignatureException,
    SubscriptionPlanNotFoundException,
    OrderNotFoundException,
    RazorpayAPIException,
)

logger = structlog.get_logger()


class RazorpayService:
    """
    Service class for Razorpay payment integration.
    
    Provides methods for:
    - Plan management
    - Order creation and management
    - Payment verification
    - Webhook processing
    - Transaction tracking
    - Audit logging
    """

    def __init__(self, db: Session):
        """Initialize Razorpay service with database session."""
        self.db = db
        self.client = razorpay.Client(
            auth=(settings.RAZORPAY_KEY_ID, settings.RAZORPAY_KEY_SECRET)
        )
        self.logger = logger.bind(service="razorpay")

    # Subscription Plan Helper Methods
    def get_subscription_plan(self, plan_id: str) -> Optional[SubscriptionPlan]:
        """Get a subscription plan by ID."""
        return self.db.query(SubscriptionPlan).filter(SubscriptionPlan.id == plan_id).first()

    def get_active_subscription_plans(self) -> List[SubscriptionPlan]:
        """Get all active subscription plans."""
        return self.db.query(SubscriptionPlan).filter(SubscriptionPlan.is_active == True).all()

    # Order Management
    def create_order(self, order_data: OrderCreate, user_id: str, user_type: str) -> Order:
        """Create a new Razorpay order."""
        try:
            # Get subscription plan details
            subscription_plan = self.get_subscription_plan(order_data.subscription_plan_id)
            if not subscription_plan:
                raise SubscriptionPlanNotFoundException(f"Subscription plan not found: {order_data.subscription_plan_id}")

            if not subscription_plan.is_active:
                raise SubscriptionPlanNotFoundException(f"Subscription plan is not active: {order_data.subscription_plan_id}")
            
            # Generate order ID and receipt
            order_id = str(uuid.uuid4())
            receipt = f"order_{order_id[:8]}"
            
            # Create Razorpay order
            razorpay_order_data = {
                "amount": int(subscription_plan.price * 100),  # Convert to paise
                "currency": "INR",  # Default currency for Razorpay
                "receipt": receipt,
                "notes": order_data.razorpay_notes or {}
            }
            
            razorpay_order = self.client.order.create(razorpay_order_data)
            
            # Create order in database
            order = Order(
                id=order_id,
                razorpay_order_id=razorpay_order["id"],
                subscription_plan_id=order_data.subscription_plan_id,
                user_id=user_id,
                user_email=order_data.user_email,
                user_type=user_type,
                amount=subscription_plan.price,
                currency="INR",
                status=OrderStatus.CREATED,
                razorpay_receipt=receipt,
                razorpay_notes=order_data.razorpay_notes,
                expires_at=datetime.utcnow() + timedelta(hours=24)  # 24 hour expiry
            )
            
            self.db.add(order)
            self.db.commit()
            self.db.refresh(order)
            
            # Create audit log
            self._create_audit_log(
                entity_type=AuditEntityType.ORDER,
                entity_id=order_id,
                action=AuditAction.ORDER_CREATED,
                user_id=user_id,
                new_values=order.to_dict(),
                description=f"Created order for subscription plan: {subscription_plan.name}"
            )
            
            self.logger.info(
                "Order created successfully",
                order_id=order_id,
                razorpay_order_id=razorpay_order["id"],
                subscription_plan_id=order_data.subscription_plan_id,
                user_id=user_id
            )
            
            return order
            
        except (BadRequestError, GatewayError, ServerError) as e:
            self.logger.error("Razorpay API error", error=str(e))
            raise RazorpayAPIException(f"Payment gateway error: {str(e)}")
        except SQLAlchemyError as e:
            self.db.rollback()
            self.logger.error("Database error creating order", error=str(e))
            raise OrderCreationException(f"Failed to create order: {str(e)}")

    def get_order(self, order_id: str) -> Optional[Order]:
        """Get an order by ID."""
        return self.db.query(Order).filter(Order.id == order_id).first()

    def get_order_by_razorpay_id(self, razorpay_order_id: str) -> Optional[Order]:
        """Get an order by Razorpay order ID."""
        return self.db.query(Order).filter(Order.razorpay_order_id == razorpay_order_id).first()

    # Payment Verification
    def verify_payment(self, verification_data: PaymentVerification, user_id: str = None) -> Transaction:
        """Verify payment signature and create transaction record."""
        try:
            # Verify signature
            if not self._verify_payment_signature(verification_data):
                raise InvalidSignatureException("Invalid payment signature")

            # Get order
            order = self.get_order_by_razorpay_id(verification_data.razorpay_order_id)
            if not order:
                raise OrderNotFoundException(f"Order not found: {verification_data.razorpay_order_id}")
            
            # Get payment details from Razorpay
            payment = self.client.payment.fetch(verification_data.razorpay_payment_id)
            
            # Create transaction record
            transaction_id = str(uuid.uuid4())
            transaction = Transaction(
                id=transaction_id,
                razorpay_payment_id=verification_data.razorpay_payment_id,
                order_id=order.id,
                amount=Decimal(payment["amount"]) / 100,  # Convert from paise
                currency=payment["currency"],
                status=TransactionStatus.CAPTURED if payment["status"] == "captured" else TransactionStatus.AUTHORIZED,
                transaction_type=TransactionType.PAYMENT,
                payment_method=payment.get("method"),
                payment_method_details=payment,
                razorpay_signature=verification_data.razorpay_signature,
                razorpay_order_id=verification_data.razorpay_order_id,
                razorpay_fee=Decimal(payment.get("fee", 0)) / 100 if payment.get("fee") else None,
                razorpay_tax=Decimal(payment.get("tax", 0)) / 100 if payment.get("tax") else None,
                gateway_response=payment,
                processed_at=datetime.utcnow()
            )
            
            # Update order status
            order.status = OrderStatus.PAID
            order.paid_at = datetime.utcnow()

            self.db.add(transaction)
            self.db.commit()
            self.db.refresh(transaction)

            # Create or update subscription after successful payment
            self._create_subscription_after_payment(order)
            
            # Create audit logs
            self._create_audit_log(
                entity_type=AuditEntityType.TRANSACTION,
                entity_id=transaction_id,
                action=AuditAction.PAYMENT_COMPLETED,
                user_id=user_id,
                new_values=transaction.to_dict(),
                description=f"Payment completed for order: {order.id}"
            )
            
            self._create_audit_log(
                entity_type=AuditEntityType.ORDER,
                entity_id=order.id,
                action=AuditAction.ORDER_UPDATED,
                user_id=user_id,
                new_values=order.to_dict(),
                description="Order marked as paid"
            )
            
            self.logger.info(
                "Payment verified successfully",
                transaction_id=transaction_id,
                payment_id=verification_data.razorpay_payment_id,
                order_id=order.id
            )
            
            return transaction
            
        except (BadRequestError, GatewayError, ServerError) as e:
            self.logger.error("Razorpay API error during verification", error=str(e))
            raise RazorpayAPIException(f"Payment verification failed: {str(e)}")
        except SQLAlchemyError as e:
            self.db.rollback()
            self.logger.error("Database error during payment verification", error=str(e))
            raise PaymentVerificationException(f"Failed to verify payment: {str(e)}")

    def _verify_payment_signature(self, verification_data: PaymentVerification) -> bool:
        """Verify Razorpay payment signature."""
        try:
            # Create signature string
            signature_string = f"{verification_data.razorpay_order_id}|{verification_data.razorpay_payment_id}"
            
            # Generate expected signature
            expected_signature = hmac.new(
                settings.RAZORPAY_KEY_SECRET.encode(),
                signature_string.encode(),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(expected_signature, verification_data.razorpay_signature)
            
        except Exception as e:
            self.logger.error("Error verifying payment signature", error=str(e))
            return False

    # Webhook Processing
    def process_webhook(self, payload: dict, signature: str) -> WebhookEvent:
        """Process Razorpay webhook event."""
        try:
            # Verify webhook signature
            if not self._verify_webhook_signature(payload, signature):
                raise InvalidSignatureException("Invalid webhook signature")

            # Extract event information
            event_type = payload.get("event")
            event_id = payload.get("payload", {}).get("payment", {}).get("entity", {}).get("id", str(uuid.uuid4()))
            entity = payload.get("payload", {}).get("payment", {}).get("entity", {})

            # Check if event already processed (idempotency)
            existing_event = self.db.query(WebhookEvent).filter(
                WebhookEvent.razorpay_event_id == event_id
            ).first()

            if existing_event:
                self.logger.info("Webhook event already processed", event_id=event_id)
                return existing_event

            # Create webhook event record
            webhook_event_id = str(uuid.uuid4())
            webhook_event = WebhookEvent(
                id=webhook_event_id,
                razorpay_event_id=event_id,
                event_type=WebhookEventType(event_type) if event_type in [e.value for e in WebhookEventType] else None,
                status=WebhookEventStatus.PROCESSING,
                event_payload=payload,
                signature=signature,
                entity_type=entity.get("entity"),
                entity_id=entity.get("id")
            )

            self.db.add(webhook_event)
            self.db.commit()

            # Process the event based on type
            try:
                self._process_webhook_event(webhook_event, payload)
                webhook_event.mark_as_processed({"processed_successfully": True})

            except Exception as e:
                webhook_event.mark_as_failed(str(e))
                self.logger.error("Failed to process webhook event", event_id=event_id, error=str(e))

            self.db.commit()

            # Create audit log
            self._create_audit_log(
                entity_type=AuditEntityType.WEBHOOK_EVENT,
                entity_id=webhook_event_id,
                action=AuditAction.WEBHOOK_PROCESSED,
                description=f"Processed webhook event: {event_type}",
                audit_metadata={"event_type": event_type, "entity_id": entity.get("id")}
            )

            return webhook_event

        except SQLAlchemyError as e:
            self.db.rollback()
            self.logger.error("Database error processing webhook", error=str(e))
            raise WebhookProcessingException(f"Failed to process webhook: {str(e)}")

    def _verify_webhook_signature(self, payload: dict, signature: str) -> bool:
        """Verify Razorpay webhook signature."""
        try:
            import json

            # Create signature string from payload
            payload_string = json.dumps(payload, separators=(',', ':'), sort_keys=True)

            # Generate expected signature
            expected_signature = hmac.new(
                settings.RAZORPAY_WEBHOOK_SECRET.encode(),
                payload_string.encode(),
                hashlib.sha256
            ).hexdigest()

            return hmac.compare_digest(expected_signature, signature)

        except Exception as e:
            self.logger.error("Error verifying webhook signature", error=str(e))
            return False

    def _process_webhook_event(self, webhook_event: WebhookEvent, payload: dict) -> None:
        """Process specific webhook event types."""
        event_type = webhook_event.event_type
        entity = payload.get("payload", {}).get("payment", {}).get("entity", {})

        if event_type == WebhookEventType.PAYMENT_CAPTURED:
            self._handle_payment_captured(entity)
        elif event_type == WebhookEventType.PAYMENT_FAILED:
            self._handle_payment_failed(entity)
        elif event_type == WebhookEventType.ORDER_PAID:
            self._handle_order_paid(entity)
        else:
            self.logger.info("Unhandled webhook event type", event_type=event_type)

    def _handle_payment_captured(self, payment_entity: dict) -> None:
        """Handle payment.captured webhook event."""
        payment_id = payment_entity.get("id")
        order_id = payment_entity.get("order_id")

        # Find existing transaction or create new one
        transaction = self.db.query(Transaction).filter(
            Transaction.razorpay_payment_id == payment_id
        ).first()

        if transaction:
            transaction.status = TransactionStatus.CAPTURED
            transaction.processed_at = datetime.utcnow()
            transaction.gateway_response = payment_entity

        # Update order status
        order = self.get_order_by_razorpay_id(order_id)
        if order:
            order.status = OrderStatus.PAID
            order.paid_at = datetime.utcnow()

            # Create subscription after successful payment
            self._create_subscription_after_payment(order)

    def _handle_payment_failed(self, payment_entity: dict) -> None:
        """Handle payment.failed webhook event."""
        payment_id = payment_entity.get("id")
        order_id = payment_entity.get("order_id")

        # Update transaction status
        transaction = self.db.query(Transaction).filter(
            Transaction.razorpay_payment_id == payment_id
        ).first()

        if transaction:
            transaction.status = TransactionStatus.FAILED
            transaction.error_code = payment_entity.get("error_code")
            transaction.error_description = payment_entity.get("error_description")
            transaction.gateway_response = payment_entity

        # Update order status
        order = self.get_order_by_razorpay_id(order_id)
        if order:
            order.status = OrderStatus.FAILED

    def _handle_order_paid(self, order_entity: dict) -> None:
        """Handle order.paid webhook event."""
        razorpay_order_id = order_entity.get("id")

        order = self.get_order_by_razorpay_id(razorpay_order_id)
        if order:
            order.status = OrderStatus.PAID
            order.paid_at = datetime.utcnow()

            # Create subscription after successful payment
            self._create_subscription_after_payment(order)

    # Subscription Management After Payment
    def _create_subscription_after_payment(self, order: Order) -> None:
        """Create or update subscription after successful payment using SubscriptionService."""
        try:
            # Import here to avoid circular imports
            from app.services.subscription_service import SubscriptionService
            subscription_service = SubscriptionService(self.db)

            # Validate user type consistency
            self._validate_user_type_consistency(order)

            if order.user_type == "individual":
                subscription_service.assign_subscription_to_user(order.user_id, order.subscription_plan_id)
                self.logger.info("User subscription assigned after payment", user_id=order.user_id, plan_id=order.subscription_plan_id)

                # Award referral tokens if this user was referred
                self._process_referral_tokens_for_paid_plan(order.user_type, order.user_id, order.subscription_plan_id)

            elif order.user_type == "organization":
                subscription_service.assign_subscription_to_organization(order.user_id, order.subscription_plan_id)
                self.logger.info("Organization subscription assigned after payment", organization_id=order.user_id, plan_id=order.subscription_plan_id)

                # Award referral tokens if this organization was referred
                self._process_referral_tokens_for_paid_plan(order.user_type, order.user_id, order.subscription_plan_id)

            else:
                self.logger.error("Unknown user type for subscription creation", user_type=order.user_type, order_id=order.id)
                raise ValueError(f"Invalid user type: {order.user_type}")

            # Create audit log for payment completion
            self._create_audit_log(
                entity_type=AuditEntityType.SUBSCRIPTION,
                entity_id=order.user_id,
                action=AuditAction.PAYMENT_COMPLETED,
                user_id=order.user_id,
                description=f"Subscription activated after successful payment for plan: {order.subscription_plan_id}",
                audit_metadata={"order_id": order.id, "plan_id": order.subscription_plan_id, "user_type": order.user_type}
            )

        except Exception as e:
            self.logger.error("Failed to create subscription after payment", order_id=order.id, error=str(e))
            # Don't raise exception as payment was successful, just log the error
            # The payment is complete, but subscription assignment failed
            # This should be handled by admin intervention or retry mechanism

    def _process_referral_tokens_for_paid_plan(self, user_type: str, user_id: str, plan_id: str) -> None:
        """
        Process referral token awards when a user purchases a paid plan.

        Args:
            user_type: "individual" or "organization"
            user_id: The user/organization ID
            plan_id: The subscription plan ID
        """
        try:
            # Import here to avoid circular imports
            from app.services.referral_service import ReferralService
            from app.models.referral import EntityType

            referral_service = ReferralService(self.db)

            # Determine entity type
            entity_type = EntityType.USER if user_type == "individual" else EntityType.ORGANIZATION

            # Process referral token awards
            success = referral_service.process_paid_plan_purchase(entity_type, user_id, plan_id)

            if success:
                self.logger.info("Referral tokens awarded for paid plan purchase",
                               user_type=user_type, user_id=user_id, plan_id=plan_id)
            else:
                self.logger.debug("No referral tokens to award (user not referred or already processed)",
                                user_type=user_type, user_id=user_id, plan_id=plan_id)

        except Exception as e:
            self.logger.error("Failed to process referral tokens for paid plan",
                            user_type=user_type, user_id=user_id, plan_id=plan_id, error=str(e))
            # Don't raise exception as payment was successful
            # Referral token processing failure shouldn't affect the main payment flow

    def _validate_user_type_consistency(self, order: Order) -> None:
        """Validate that the order user type matches the actual user registration type."""
        try:
            if order.user_type == "individual":
                from app.models.user import User
                user = self.db.query(User).filter(User.id == order.user_id).first()
                if not user:
                    raise ValueError(f"Individual user not found: {order.user_id}")
            elif order.user_type == "organization":
                from app.models.organization import Organization
                org = self.db.query(Organization).filter(Organization.id == order.user_id).first()
                if not org:
                    raise ValueError(f"Organization not found: {order.user_id}")
            else:
                raise ValueError(f"Invalid user type: {order.user_type}")

        except Exception as e:
            self.logger.error("User type validation failed", order_id=order.id, user_type=order.user_type, error=str(e))
            raise

    # Health Check Methods
    def check_razorpay_health(self) -> Dict[str, Any]:
        """Check Razorpay API health."""
        try:
            start_time = datetime.utcnow()

            # Try to fetch a dummy order to test API connectivity
            try:
                # This will fail but tells us if API is accessible
                self.client.order.fetch("dummy_order_id")
            except BadRequestError:
                # Expected error for dummy ID - API is accessible
                pass

            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds() * 1000

            return {
                "status": "healthy",
                "api_accessible": True,
                "response_time_ms": response_time
            }

        except Exception as e:
            self.logger.error("Razorpay health check failed", error=str(e))
            return {
                "status": "unhealthy",
                "api_accessible": False,
                "error": str(e)
            }

    # Utility Methods
    def _create_audit_log(
        self,
        entity_type: AuditEntityType,
        entity_id: str,
        action: AuditAction,
        user_id: str = None,
        old_values: dict = None,
        new_values: dict = None,
        description: str = None,
        audit_metadata: dict = None
    ) -> None:
        """Create an audit log entry."""
        try:
            audit_log = AuditLog.create_log(
                entity_type=entity_type,
                entity_id=entity_id,
                action=action,
                user_id=user_id,
                old_values=old_values,
                new_values=new_values,
                description=description,
                audit_metadata=audit_metadata
            )

            self.db.add(audit_log)
            # Note: Commit is handled by the calling method

        except Exception as e:
            self.logger.error("Failed to create audit log", error=str(e))
            # Don't raise exception for audit log failures
