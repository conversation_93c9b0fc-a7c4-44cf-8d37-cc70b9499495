"""add_referral_system

Revision ID: add_referral_system
Revises: f6a22399580b
Create Date: 2025-08-06 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_referral_system'
down_revision: Union[str, None] = 'f6a22399580b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add referral_code columns to users and organizations tables
    op.add_column('users', sa.Column('referral_code', sa.String(), nullable=True))
    op.create_index(op.f('ix_users_referral_code'), 'users', ['referral_code'], unique=True)
    
    op.add_column('organizations', sa.Column('referral_code', sa.String(), nullable=True))
    op.create_index(op.f('ix_organizations_referral_code'), 'organizations', ['referral_code'], unique=True)
    
    # Create referral_codes table
    op.create_table('referral_codes',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('code', sa.String(), nullable=False),
        sa.Column('entity_type', sa.Enum('USER', 'ORGANIZATION', name='entitytype'), nullable=False),
        sa.Column('entity_id', sa.String(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('code'),
        sa.UniqueConstraint('entity_type', 'entity_id', name='uq_entity_referral_code')
    )
    op.create_index(op.f('ix_referral_codes_code'), 'referral_codes', ['code'], unique=True)
    op.create_index('idx_referral_codes_entity', 'referral_codes', ['entity_type', 'entity_id'], unique=False)
    
    # Create referral_relationships table
    op.create_table('referral_relationships',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('referrer_code_id', sa.String(), nullable=False),
        sa.Column('referrer_entity_type', sa.Enum('USER', 'ORGANIZATION', name='entitytype'), nullable=False),
        sa.Column('referrer_entity_id', sa.String(), nullable=False),
        sa.Column('referee_entity_type', sa.Enum('USER', 'ORGANIZATION', name='entitytype'), nullable=False),
        sa.Column('referee_entity_id', sa.String(), nullable=False),
        sa.Column('referral_date', sa.DateTime(), nullable=True),
        sa.Column('tokens_awarded', sa.Boolean(), nullable=True),
        sa.Column('tokens_awarded_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['referrer_code_id'], ['referral_codes.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('referee_entity_type', 'referee_entity_id', name='uq_referee_referral')
    )
    op.create_index('idx_referral_relationships_referrer', 'referral_relationships', ['referrer_entity_type', 'referrer_entity_id'], unique=False)
    op.create_index('idx_referral_relationships_referee', 'referral_relationships', ['referee_entity_type', 'referee_entity_id'], unique=False)
    
    # Create token_transactions table
    op.create_table('token_transactions',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('entity_type', sa.Enum('USER', 'ORGANIZATION', name='entitytype'), nullable=False),
        sa.Column('entity_id', sa.String(), nullable=False),
        sa.Column('transaction_type', sa.Enum('REFERRAL_BONUS_REFERRER', 'REFERRAL_BONUS_REFEREE', 'SUBSCRIPTION_PURCHASE', 'BONUS_AWARD', 'PENALTY', name='tokentransactiontype'), nullable=False),
        sa.Column('amount', sa.Integer(), nullable=False),
        sa.Column('balance_after', sa.Integer(), nullable=False),
        sa.Column('reference_id', sa.String(), nullable=True),
        sa.Column('reference_type', sa.String(), nullable=True),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_token_transactions_entity', 'token_transactions', ['entity_type', 'entity_id'], unique=False)
    op.create_index('idx_token_transactions_type', 'token_transactions', ['transaction_type'], unique=False)
    op.create_index('idx_token_transactions_created', 'token_transactions', ['created_at'], unique=False)
    op.create_index('idx_token_transactions_reference', 'token_transactions', ['reference_type', 'reference_id'], unique=False)
    
    # Create referral_stats table
    op.create_table('referral_stats',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('entity_type', sa.Enum('USER', 'ORGANIZATION', name='entitytype'), nullable=False),
        sa.Column('entity_id', sa.String(), nullable=False),
        sa.Column('total_referrals', sa.Integer(), nullable=True),
        sa.Column('successful_referrals', sa.Integer(), nullable=True),
        sa.Column('total_tokens_earned', sa.Integer(), nullable=True),
        sa.Column('current_token_balance', sa.Integer(), nullable=True),
        sa.Column('last_referral_date', sa.DateTime(), nullable=True),
        sa.Column('last_updated', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('entity_type', 'entity_id', name='uq_entity_referral_stats')
    )
    op.create_index('idx_referral_stats_entity', 'referral_stats', ['entity_type', 'entity_id'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    # Drop referral tables
    op.drop_table('referral_stats')
    op.drop_table('token_transactions')
    op.drop_table('referral_relationships')
    op.drop_table('referral_codes')
    
    # Drop enum types
    op.execute('DROP TYPE IF EXISTS tokentransactiontype')
    op.execute('DROP TYPE IF EXISTS entitytype')
    
    # Remove referral_code columns
    op.drop_index(op.f('ix_organizations_referral_code'), table_name='organizations')
    op.drop_column('organizations', 'referral_code')
    
    op.drop_index(op.f('ix_users_referral_code'), table_name='users')
    op.drop_column('users', 'referral_code')
